// Global variables
let keyauthApp = null;
let currentTab = 'home';
let apiKey = '';
let isApiKeySet = false;
let isAuthenticated = false;
let user = null;

// KeyAuth Configuration - Replace with your actual KeyAuth credentials
const KEYAUTH_CONFIG = {
    name: "SSSS", // Your app name
    ownerid: "zOBkfpmZB1", // Your KeyAuth owner ID
    version: "1.2", // Your app version
    url: "https://keyauth.win/api/1.2/" // KeyAuth API URL
};

// Simple KeyAuth class for demo purposes
class KeyAuth {
    constructor(config) {
        this.config = config;
        this.sessionid = null;
        this.initialized = false;
    }

    async init() {
        // Initialize KeyAuth session
        this.initialized = true;
        return true;
    }

    async login(username, password) {
        // Simulate KeyAuth login API call
        // In real implementation, make actual API call to KeyAuth
        return { success: true, message: "Login successful" };
    }

    async register(username, password, license) {
        // Simulate KeyAuth register API call
        return { success: true, message: "Registration successful" };
    }

    async license(key) {
        // Simulate KeyAuth license API call
        return { success: true, message: "License activated" };
    }
}

// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const navbar = document.getElementById('navbar');
const navLinks = document.querySelectorAll('.nav-link');
const tabContents = document.querySelectorAll('.tab-content');
const authLoading = document.getElementById('auth-loading');
const loginButton = document.getElementById('login-button');
const userProfile = document.getElementById('user-profile');
const chatbotAuthGuard = document.getElementById('chatbot-auth-guard');
const chatbotContainer = document.getElementById('chatbot-container');
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const apiKeySection = document.getElementById('apiKeySection');
const chatInput = document.getElementById('chatInput');
const connectionStatus = document.getElementById('connection-status');

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    showLoading();
    await initializeKeyAuth();
    initializeNavigation();
    initializeChatbot();
    initializeKeyboardShortcuts();
    hideLoading();

    // Check if API key is stored
    const storedApiKey = localStorage.getItem('gemini_api_key');
    if (storedApiKey && isAuthenticated) {
        apiKey = storedApiKey;
        setApiKeySuccess();
    }
});

// Loading functions
function showLoading() {
    loadingScreen.style.display = 'flex';
    navbar.style.display = 'none';
}

function hideLoading() {
    loadingScreen.style.opacity = '0';
    setTimeout(() => {
        loadingScreen.style.display = 'none';
        navbar.style.display = 'block';
    }, 500);
}

// KeyAuth Integration
async function initializeKeyAuth() {
    try {
        authLoading.style.display = 'flex';

        // Initialize KeyAuth
        keyauthApp = new KeyAuth(KEYAUTH_CONFIG);

        // Check if user session exists
        const savedSession = localStorage.getItem('keyauth_session');
        if (savedSession) {
            try {
                const sessionData = JSON.parse(savedSession);
                user = sessionData;
                isAuthenticated = true;
                updateUIForAuthenticatedUser();
            } catch (error) {
                localStorage.removeItem('keyauth_session');
                updateUIForUnauthenticatedUser();
            }
        } else {
            updateUIForUnauthenticatedUser();
        }
    } catch (error) {
        console.error('KeyAuth initialization error:', error);
        showNotification('Authentication service unavailable. Some features may be limited.', 'error');
        updateUIForUnauthenticatedUser();
    } finally {
        authLoading.style.display = 'none';
    }
}

function updateUIForAuthenticatedUser() {
    loginButton.style.display = 'none';
    userProfile.style.display = 'flex';
    
    // Update user profile
    document.getElementById('user-avatar').src = user.picture || 'https://via.placeholder.com/32';
    document.getElementById('user-name').textContent = user.name || user.email;
    document.getElementById('chat-user-name').textContent = user.name || user.email;
    
    // Show chatbot if on chatbot tab
    if (currentTab === 'chatbot') {
        chatbotAuthGuard.style.display = 'none';
        chatbotContainer.style.display = 'block';
    }
}

function updateUIForUnauthenticatedUser() {
    loginButton.style.display = 'block';
    userProfile.style.display = 'none';
    
    // Hide chatbot and show auth guard
    if (currentTab === 'chatbot') {
        chatbotAuthGuard.style.display = 'flex';
        chatbotContainer.style.display = 'none';
    }
}

async function login() {
    // Show login modal
    showLoginModal();
}

function showLoginModal() {
    // Create login modal
    const modal = document.createElement('div');
    modal.className = 'auth-modal';
    modal.innerHTML = `
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h3><i class="fas fa-sign-in-alt"></i> Login to Gemini AI</h3>
                <button class="close-modal" onclick="closeAuthModal()">&times;</button>
            </div>
            <div class="auth-modal-body">
                <div class="auth-tabs">
                    <button class="auth-tab active" onclick="switchAuthTab('login')">Login</button>
                    <button class="auth-tab" onclick="switchAuthTab('register')">Register</button>
                    <button class="auth-tab" onclick="switchAuthTab('license')">License Key</button>
                </div>

                <form id="login-form" class="auth-form active">
                    <div class="form-group">
                        <input type="text" id="login-username" placeholder="Username" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="login-password" placeholder="Password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </form>

                <form id="register-form" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="register-username" placeholder="Username" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="register-password" placeholder="Password" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="register-license" placeholder="License Key" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Register
                    </button>
                </form>

                <form id="license-form" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="license-key" placeholder="License Key" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key"></i> Activate License
                    </button>
                </form>
            </div>
        </div>
    `;

    // Add modal styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;

    document.body.appendChild(modal);

    // Add event listeners
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    document.getElementById('register-form').addEventListener('submit', handleRegister);
    document.getElementById('license-form').addEventListener('submit', handleLicense);
}

async function logout() {
    try {
        // Clear local storage
        localStorage.removeItem('keyauth_session');
        localStorage.removeItem('gemini_api_key');
        apiKey = '';
        isApiKeySet = false;
        isAuthenticated = false;
        user = null;

        updateUIForUnauthenticatedUser();
        showNotification('Logged out successfully', 'success');

        // Reset chatbot if on chatbot tab
        if (currentTab === 'chatbot') {
            chatbotAuthGuard.style.display = 'flex';
            chatbotContainer.style.display = 'none';
        }
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Logout failed. Please try again.', 'error');
    }
}

// KeyAuth Authentication Handlers
async function handleLogin(event) {
    event.preventDefault();
    const username = document.getElementById('login-username').value;
    const password = document.getElementById('login-password').value;

    try {
        // Simulate KeyAuth login (replace with actual KeyAuth API call)
        const response = await simulateKeyAuthLogin(username, password);
        if (response.success) {
            user = {
                username: username,
                email: `${username}@example.com`,
                picture: `https://ui-avatars.com/api/?name=${username}&background=4f46e5&color=fff`
            };
            isAuthenticated = true;
            localStorage.setItem('keyauth_session', JSON.stringify(user));
            updateUIForAuthenticatedUser();
            closeAuthModal();
            showNotification('Login successful!', 'success');
        } else {
            showNotification(response.message || 'Login failed', 'error');
        }
    } catch (error) {
        showNotification('Login error: ' + error.message, 'error');
    }
}

async function handleRegister(event) {
    event.preventDefault();
    const username = document.getElementById('register-username').value;
    const password = document.getElementById('register-password').value;
    const license = document.getElementById('register-license').value;

    try {
        // Simulate KeyAuth registration (replace with actual KeyAuth API call)
        const response = await simulateKeyAuthRegister(username, password, license);
        if (response.success) {
            user = {
                username: username,
                email: `${username}@example.com`,
                picture: `https://ui-avatars.com/api/?name=${username}&background=4f46e5&color=fff`
            };
            isAuthenticated = true;
            localStorage.setItem('keyauth_session', JSON.stringify(user));
            updateUIForAuthenticatedUser();
            closeAuthModal();
            showNotification('Registration successful!', 'success');
        } else {
            showNotification(response.message || 'Registration failed', 'error');
        }
    } catch (error) {
        showNotification('Registration error: ' + error.message, 'error');
    }
}

async function handleLicense(event) {
    event.preventDefault();
    const license = document.getElementById('license-key').value;

    try {
        // Simulate KeyAuth license activation (replace with actual KeyAuth API call)
        const response = await simulateKeyAuthLicense(license);
        if (response.success) {
            user = {
                username: 'Licensed User',
                email: '<EMAIL>',
                picture: `https://ui-avatars.com/api/?name=Licensed+User&background=4f46e5&color=fff`
            };
            isAuthenticated = true;
            localStorage.setItem('keyauth_session', JSON.stringify(user));
            updateUIForAuthenticatedUser();
            closeAuthModal();
            showNotification('License activated successfully!', 'success');
        } else {
            showNotification(response.message || 'License activation failed', 'error');
        }
    } catch (error) {
        showNotification('License error: ' + error.message, 'error');
    }
}

// Simulate KeyAuth API calls (replace with actual KeyAuth integration)
async function simulateKeyAuthLogin(username, password) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple validation for demo
    if (username && password) {
        return { success: true, message: 'Login successful' };
    } else {
        return { success: false, message: 'Invalid credentials' };
    }
}

async function simulateKeyAuthRegister(username, password, license) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple validation for demo
    if (username && password && license) {
        return { success: true, message: 'Registration successful' };
    } else {
        return { success: false, message: 'All fields are required' };
    }
}

async function simulateKeyAuthLicense(license) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple validation for demo
    if (license && license.length > 5) {
        return { success: true, message: 'License activated' };
    } else {
        return { success: false, message: 'Invalid license key' };
    }
}

// Modal management functions
function switchAuthTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.auth-tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.textContent.toLowerCase().includes(tabName)) {
            tab.classList.add('active');
        }
    });

    // Update forms
    document.querySelectorAll('.auth-form').forEach(form => {
        form.classList.remove('active');
    });

    document.getElementById(`${tabName}-form`).classList.add('active');
}

function closeAuthModal() {
    const modal = document.querySelector('.auth-modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Navigation functionality
function initializeNavigation() {
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            switchTab(tabName);
        });
    });
}

function switchTab(tabName) {
    // Update navigation
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-tab') === tabName) {
            link.classList.add('active');
        }
    });
    
    // Update content
    tabContents.forEach(content => {
        content.classList.remove('active');
        if (content.id === tabName) {
            content.classList.add('active');
        }
    });
    
    currentTab = tabName;
    
    // Handle chatbot tab authentication
    if (tabName === 'chatbot') {
        if (isAuthenticated) {
            chatbotAuthGuard.style.display = 'none';
            chatbotContainer.style.display = 'block';
        } else {
            chatbotAuthGuard.style.display = 'flex';
            chatbotContainer.style.display = 'none';
        }
    }
}

// Chatbot functionality
function initializeChatbot() {
    // Enter key to send message
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
}

// Keyboard shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R + K = Reset API Key
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'K') {
            e.preventDefault();
            if (isApiKeySet && currentTab === 'chatbot') {
                resetApiKey();
            }
        }

        // Ctrl/Cmd + L = Focus on login
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            if (!isAuthenticated) {
                login();
            }
        }

        // Escape = Close modals
        if (e.key === 'Escape') {
            closeAuthModal();
        }
    });
}

function setApiKey() {
    if (!isAuthenticated) {
        showNotification('Please log in first', 'error');
        return;
    }
    
    const apiKeyInput = document.getElementById('apiKey');
    const inputKey = apiKeyInput.value.trim();
    
    if (!inputKey) {
        showNotification('Please enter a valid API key', 'error');
        return;
    }
    
    // Basic validation for Gemini API key format
    if (!inputKey.startsWith('AIza') || inputKey.length < 30) {
        showNotification('Invalid API key format. Please check your key.', 'error');
        return;
    }
    
    apiKey = inputKey;
    localStorage.setItem('gemini_api_key', apiKey);
    setApiKeySuccess();
    showNotification('API key set successfully!', 'success');
}

function setApiKeySuccess() {
    isApiKeySet = true;
    apiKeySection.style.display = 'none';
    chatInput.style.display = 'flex';
    connectionStatus.textContent = 'Connected';
    connectionStatus.style.color = '#10b981';

    // Add reset button to chat header
    addApiKeyResetButton();
}

function addApiKeyResetButton() {
    // Check if reset button already exists
    if (document.getElementById('reset-api-key-btn')) {
        return;
    }

    const chatHeader = document.querySelector('.chat-header');
    const statusIndicator = document.querySelector('.status-indicator');

    if (chatHeader && statusIndicator) {
        const resetButton = document.createElement('button');
        resetButton.id = 'reset-api-key-btn';
        resetButton.className = 'btn btn-small reset-api-btn';
        resetButton.innerHTML = '<i class="fas fa-key"></i> Reset API Key';
        resetButton.onclick = resetApiKey;

        // Insert before status indicator
        chatHeader.insertBefore(resetButton, statusIndicator);
    }
}

function resetApiKey() {
    // Confirm reset
    if (confirm('Are you sure you want to reset your Gemini API key? You will need to enter it again.')) {
        // Clear API key
        apiKey = '';
        isApiKeySet = false;
        localStorage.removeItem('gemini_api_key');

        // Reset UI
        apiKeySection.style.display = 'block';
        chatInput.style.display = 'none';
        connectionStatus.textContent = 'API Key Required';
        connectionStatus.style.color = '#ef4444';

        // Clear API key input
        const apiKeyInput = document.getElementById('apiKey');
        if (apiKeyInput) {
            apiKeyInput.value = '';
        }

        // Remove reset button
        const resetButton = document.getElementById('reset-api-key-btn');
        if (resetButton) {
            resetButton.remove();
        }

        // Add system message to chat
        addSystemMessage('API key has been reset. Please enter a new key to continue chatting.');

        showNotification('API key reset successfully. Please enter a new key.', 'info');
    }
}

// Add system message to chat
function addSystemMessage(content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message system-message';

    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = '<i class="fas fa-cog"></i>';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    const textP = document.createElement('p');
    textP.textContent = content;

    const timeSpan = document.createElement('span');
    timeSpan.className = 'message-time';
    timeSpan.textContent = new Date().toLocaleTimeString();

    contentDiv.appendChild(textP);
    contentDiv.appendChild(timeSpan);

    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);

    if (chatMessages) {
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

async function sendMessage() {
    if (!isAuthenticated) {
        showNotification('Please log in first', 'error');
        return;
    }
    
    if (!isApiKeySet) {
        showNotification('Please set your API key first', 'error');
        return;
    }
    
    const message = messageInput.value.trim();
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    messageInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // Call Gemini API
        const response = await callGeminiAPI(message);
        hideTypingIndicator();
        addMessage(response, 'bot');
    } catch (error) {
        hideTypingIndicator();
        addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        console.error('Error calling Gemini API:', error);
        showNotification('Error communicating with AI. Please check your API key.', 'error');
    }
}

async function callGeminiAPI(message) {
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`;
    
    const requestBody = {
        contents: [{
            parts: [{
                text: message
            }]
        }],
        generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
        }
    };
    
    const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }
    
    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
    } else {
        throw new Error('Invalid response format from API');
    }
}

function addMessage(content, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    
    if (sender === 'bot') {
        avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';
    } else {
        // Use user's avatar if available
        if (user && user.picture) {
            avatarDiv.innerHTML = `<img src="${user.picture}" alt="User" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
        } else {
            avatarDiv.innerHTML = '<i class="fas fa-user"></i>';
        }
    }
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const textP = document.createElement('p');
    textP.textContent = content;
    
    const timeSpan = document.createElement('span');
    timeSpan.className = 'message-time';
    timeSpan.textContent = new Date().toLocaleTimeString();
    
    contentDiv.appendChild(textP);
    contentDiv.appendChild(timeSpan);
    
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-indicator';
    typingDiv.id = 'typing-indicator';
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `
        <div class="typing-indicator">
            <span>AI is typing</span>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    typingDiv.appendChild(avatarDiv);
    typingDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Contact form handler
function handleContactForm(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const name = formData.get('name');
    const email = formData.get('email');
    const category = formData.get('category');
    const message = formData.get('message');

    // Simulate form submission
    showNotification('Thank you for your message! We\'ll get back to you soon.', 'success');
    event.target.reset();

    // In a real application, you would send this data to your backend
    console.log('Contact form submission:', { name, email, category, message });
}

// Mobile menu toggle
function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');

    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#4f46e5',
        warning: '#f59e0b'
    };

    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
        max-width: 350px;
        font-weight: 500;
        font-size: 0.9rem;
    `;

    // Add animation styles if not already added
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Footer link handlers
function showPrivacyPolicy() {
    showNotification('Privacy Policy: We protect your data with enterprise-grade security.', 'info');
}

function showSecurityInfo() {
    showNotification('Security: 256-bit encryption, Auth0 authentication, and secure API calls.', 'info');
}

function showTerms() {
    showNotification('Terms of Service: Please use our platform responsibly and ethically.', 'info');
}

function showCompliance() {
    showNotification('Compliance: We adhere to GDPR, CCPA, and SOC 2 standards.', 'info');
}

function showDataPolicy() {
    showNotification('Data Policy: Your conversations are private and not stored on our servers.', 'info');
}

// Utility functions
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// Error handling for Auth0
window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('auth0')) {
        console.error('Auth0 error:', event.error);
        showNotification('Authentication error occurred. Please refresh the page.', 'error');
    }
});

// Initialize helpers and console message
function initializeHelpers() {
    console.log('🤖 Gemini AI Platform initialized successfully!');
    console.log('🔐 Auth0 authentication enabled');
    console.log('🚀 Ready for secure AI conversations');

    // Add development mode warning
    if (AUTH0_CONFIG.domain.includes('your-auth0-domain')) {
        console.warn('⚠️  Please configure your Auth0 credentials in script.js');
        showNotification('Demo mode: Please configure Auth0 for full functionality', 'warning');
    }
}

// Call initialization
initializeHelpers();
