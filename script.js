// Global variables
let auth0Client = null;
let currentTab = 'home';
let apiKey = '';
let isApiKeySet = false;
let isAuthenticated = false;
let user = null;

// Auth0 Configuration - Replace with your actual Auth0 credentials
const AUTH0_CONFIG = {
    domain: 'your-auth0-domain.auth0.com',
    clientId: 'your-auth0-client-id',
    redirectUri: window.location.origin,
    audience: 'your-api-identifier', // Optional
    scope: 'openid profile email'
};

// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const navbar = document.getElementById('navbar');
const navLinks = document.querySelectorAll('.nav-link');
const tabContents = document.querySelectorAll('.tab-content');
const authLoading = document.getElementById('auth-loading');
const loginButton = document.getElementById('login-button');
const userProfile = document.getElementById('user-profile');
const chatbotAuthGuard = document.getElementById('chatbot-auth-guard');
const chatbotContainer = document.getElementById('chatbot-container');
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const apiKeySection = document.getElementById('apiKeySection');
const chatInput = document.getElementById('chatInput');
const connectionStatus = document.getElementById('connection-status');

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    showLoading();
    await initializeAuth0();
    initializeNavigation();
    initializeChatbot();
    hideLoading();
    
    // Check if API key is stored
    const storedApiKey = localStorage.getItem('gemini_api_key');
    if (storedApiKey && isAuthenticated) {
        apiKey = storedApiKey;
        setApiKeySuccess();
    }
});

// Loading functions
function showLoading() {
    loadingScreen.style.display = 'flex';
    navbar.style.display = 'none';
}

function hideLoading() {
    loadingScreen.style.opacity = '0';
    setTimeout(() => {
        loadingScreen.style.display = 'none';
        navbar.style.display = 'block';
    }, 500);
}

// Auth0 Integration
async function initializeAuth0() {
    try {
        authLoading.style.display = 'flex';
        
        // Initialize Auth0 client
        auth0Client = await auth0.createAuth0Client(AUTH0_CONFIG);
        
        // Check if user is authenticated
        isAuthenticated = await auth0Client.isAuthenticated();
        
        if (isAuthenticated) {
            user = await auth0Client.getUser();
            updateUIForAuthenticatedUser();
        } else {
            // Check if returning from Auth0 login
            const query = window.location.search;
            if (query.includes('code=') && query.includes('state=')) {
                try {
                    await auth0Client.handleRedirectCallback();
                    isAuthenticated = await auth0Client.isAuthenticated();
                    if (isAuthenticated) {
                        user = await auth0Client.getUser();
                        updateUIForAuthenticatedUser();
                        // Clean up URL
                        window.history.replaceState({}, document.title, window.location.pathname);
                    }
                } catch (error) {
                    console.error('Error handling Auth0 callback:', error);
                    showNotification('Authentication failed. Please try again.', 'error');
                }
            } else {
                updateUIForUnauthenticatedUser();
            }
        }
    } catch (error) {
        console.error('Auth0 initialization error:', error);
        showNotification('Authentication service unavailable. Some features may be limited.', 'error');
        updateUIForUnauthenticatedUser();
    } finally {
        authLoading.style.display = 'none';
    }
}

function updateUIForAuthenticatedUser() {
    loginButton.style.display = 'none';
    userProfile.style.display = 'flex';
    
    // Update user profile
    document.getElementById('user-avatar').src = user.picture || 'https://via.placeholder.com/32';
    document.getElementById('user-name').textContent = user.name || user.email;
    document.getElementById('chat-user-name').textContent = user.name || user.email;
    
    // Show chatbot if on chatbot tab
    if (currentTab === 'chatbot') {
        chatbotAuthGuard.style.display = 'none';
        chatbotContainer.style.display = 'block';
    }
}

function updateUIForUnauthenticatedUser() {
    loginButton.style.display = 'block';
    userProfile.style.display = 'none';
    
    // Hide chatbot and show auth guard
    if (currentTab === 'chatbot') {
        chatbotAuthGuard.style.display = 'flex';
        chatbotContainer.style.display = 'none';
    }
}

async function login() {
    try {
        if (!auth0Client) {
            showNotification('Authentication service not available', 'error');
            return;
        }
        
        await auth0Client.loginWithRedirect({
            authorizationParams: {
                redirect_uri: window.location.origin
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please try again.', 'error');
    }
}

async function logout() {
    try {
        if (!auth0Client) {
            showNotification('Authentication service not available', 'error');
            return;
        }
        
        // Clear local storage
        localStorage.removeItem('gemini_api_key');
        apiKey = '';
        isApiKeySet = false;
        
        await auth0Client.logout({
            logoutParams: {
                returnTo: window.location.origin
            }
        });
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Logout failed. Please try again.', 'error');
    }
}

// Navigation functionality
function initializeNavigation() {
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            switchTab(tabName);
        });
    });
}

function switchTab(tabName) {
    // Update navigation
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-tab') === tabName) {
            link.classList.add('active');
        }
    });
    
    // Update content
    tabContents.forEach(content => {
        content.classList.remove('active');
        if (content.id === tabName) {
            content.classList.add('active');
        }
    });
    
    currentTab = tabName;
    
    // Handle chatbot tab authentication
    if (tabName === 'chatbot') {
        if (isAuthenticated) {
            chatbotAuthGuard.style.display = 'none';
            chatbotContainer.style.display = 'block';
        } else {
            chatbotAuthGuard.style.display = 'flex';
            chatbotContainer.style.display = 'none';
        }
    }
}

// Chatbot functionality
function initializeChatbot() {
    // Enter key to send message
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
}

function setApiKey() {
    if (!isAuthenticated) {
        showNotification('Please log in first', 'error');
        return;
    }
    
    const apiKeyInput = document.getElementById('apiKey');
    const inputKey = apiKeyInput.value.trim();
    
    if (!inputKey) {
        showNotification('Please enter a valid API key', 'error');
        return;
    }
    
    // Basic validation for Gemini API key format
    if (!inputKey.startsWith('AIza') || inputKey.length < 30) {
        showNotification('Invalid API key format. Please check your key.', 'error');
        return;
    }
    
    apiKey = inputKey;
    localStorage.setItem('gemini_api_key', apiKey);
    setApiKeySuccess();
    showNotification('API key set successfully!', 'success');
}

function setApiKeySuccess() {
    isApiKeySet = true;
    apiKeySection.style.display = 'none';
    chatInput.style.display = 'flex';
    connectionStatus.textContent = 'Connected';
    connectionStatus.style.color = '#10b981';
}

async function sendMessage() {
    if (!isAuthenticated) {
        showNotification('Please log in first', 'error');
        return;
    }
    
    if (!isApiKeySet) {
        showNotification('Please set your API key first', 'error');
        return;
    }
    
    const message = messageInput.value.trim();
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    messageInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // Call Gemini API
        const response = await callGeminiAPI(message);
        hideTypingIndicator();
        addMessage(response, 'bot');
    } catch (error) {
        hideTypingIndicator();
        addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        console.error('Error calling Gemini API:', error);
        showNotification('Error communicating with AI. Please check your API key.', 'error');
    }
}

async function callGeminiAPI(message) {
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`;
    
    const requestBody = {
        contents: [{
            parts: [{
                text: message
            }]
        }],
        generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
        }
    };
    
    const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }
    
    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
    } else {
        throw new Error('Invalid response format from API');
    }
}

function addMessage(content, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    
    if (sender === 'bot') {
        avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';
    } else {
        // Use user's avatar if available
        if (user && user.picture) {
            avatarDiv.innerHTML = `<img src="${user.picture}" alt="User" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
        } else {
            avatarDiv.innerHTML = '<i class="fas fa-user"></i>';
        }
    }
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const textP = document.createElement('p');
    textP.textContent = content;
    
    const timeSpan = document.createElement('span');
    timeSpan.className = 'message-time';
    timeSpan.textContent = new Date().toLocaleTimeString();
    
    contentDiv.appendChild(textP);
    contentDiv.appendChild(timeSpan);
    
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-indicator';
    typingDiv.id = 'typing-indicator';
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `
        <div class="typing-indicator">
            <span>AI is typing</span>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    typingDiv.appendChild(avatarDiv);
    typingDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Contact form handler
function handleContactForm(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const name = formData.get('name');
    const email = formData.get('email');
    const category = formData.get('category');
    const message = formData.get('message');

    // Simulate form submission
    showNotification('Thank you for your message! We\'ll get back to you soon.', 'success');
    event.target.reset();

    // In a real application, you would send this data to your backend
    console.log('Contact form submission:', { name, email, category, message });
}

// Mobile menu toggle
function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');

    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#4f46e5',
        warning: '#f59e0b'
    };

    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
        max-width: 350px;
        font-weight: 500;
        font-size: 0.9rem;
    `;

    // Add animation styles if not already added
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Footer link handlers
function showPrivacyPolicy() {
    showNotification('Privacy Policy: We protect your data with enterprise-grade security.', 'info');
}

function showSecurityInfo() {
    showNotification('Security: 256-bit encryption, Auth0 authentication, and secure API calls.', 'info');
}

function showTerms() {
    showNotification('Terms of Service: Please use our platform responsibly and ethically.', 'info');
}

function showCompliance() {
    showNotification('Compliance: We adhere to GDPR, CCPA, and SOC 2 standards.', 'info');
}

function showDataPolicy() {
    showNotification('Data Policy: Your conversations are private and not stored on our servers.', 'info');
}

// Utility functions
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// Error handling for Auth0
window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('auth0')) {
        console.error('Auth0 error:', event.error);
        showNotification('Authentication error occurred. Please refresh the page.', 'error');
    }
});

// Initialize helpers and console message
function initializeHelpers() {
    console.log('🤖 Gemini AI Platform initialized successfully!');
    console.log('🔐 Auth0 authentication enabled');
    console.log('🚀 Ready for secure AI conversations');

    // Add development mode warning
    if (AUTH0_CONFIG.domain.includes('your-auth0-domain')) {
        console.warn('⚠️  Please configure your Auth0 credentials in script.js');
        showNotification('Demo mode: Please configure Auth0 for full functionality', 'warning');
    }
}

// Call initialization
initializeHelpers();
