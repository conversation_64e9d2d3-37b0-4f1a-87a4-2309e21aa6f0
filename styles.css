/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8fafc;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #4f46e5;
}

.nav-logo i {
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
    color: #4f46e5;
    background: rgba(79, 70, 229, 0.1);
}

/* Auth Section */
.auth-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.auth-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
}

.mini-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: #1e293b;
}

.dropdown {
    position: relative;
}

.dropdown-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.dropdown-btn:hover {
    background: #f1f5f9;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    min-width: 120px;
    display: none;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    text-decoration: none;
    color: #64748b;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.dropdown-content a:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Hero Section */
.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 1rem;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #4f46e5;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* AI Visual Animation */
.ai-visual {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
}

.orbit {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

.orbit-1 {
    width: 100px;
    height: 100px;
    top: 100px;
    left: 100px;
    animation-duration: 8s;
}

.orbit-2 {
    width: 150px;
    height: 150px;
    top: 75px;
    left: 75px;
    animation-duration: 12s;
    animation-direction: reverse;
}

.orbit-3 {
    width: 200px;
    height: 200px;
    top: 50px;
    left: 50px;
    animation-duration: 15s;
}

.center-node {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: white;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1e293b;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Auth Guard */
.auth-guard {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
}

.auth-prompt {
    text-align: center;
    background: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.auth-prompt i {
    font-size: 3rem;
    color: #4f46e5;
    margin-bottom: 1rem;
}

.auth-prompt h3 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.auth-prompt p {
    color: #64748b;
    margin-bottom: 2rem;
}

/* Chatbot Styles */
.chatbot-container {
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.chat-title h2 {
    margin-bottom: 0.25rem;
}

.user-info {
    font-size: 0.9rem;
    opacity: 0.9;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reset-api-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 0.85rem;
    padding: 6px 12px;
    transition: all 0.3s ease;
}

.reset-api-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background: #f8fafc;
}

.message {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    animation: slideIn 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #e2e8f0;
    color: #64748b;
}

.message-content {
    flex: 1;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-message .message-content {
    background: #4f46e5;
    color: white;
}

.system-message .message-avatar {
    background: #f59e0b;
    color: white;
}

.system-message .message-content {
    background: #fef3c7;
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.message-time {
    font-size: 0.8rem;
    opacity: 0.7;
    display: block;
    margin-top: 0.5rem;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.chat-input-container {
    padding: 1rem;
    background: white;
    border-top: 1px solid #e2e8f0;
}

.api-key-section {
    text-align: center;
}

.api-key-input {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.api-key-input input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.api-key-input input:focus {
    outline: none;
    border-color: #4f46e5;
}

.api-help {
    color: #64748b;
    font-size: 0.9rem;
}

.api-help a {
    color: #4f46e5;
    text-decoration: none;
}

.api-help a:hover {
    text-decoration: underline;
}

.chat-input {
    display: flex;
    gap: 1rem;
}

.chat-input input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.chat-input input:focus {
    outline: none;
    border-color: #4f46e5;
}

/* About Section */
.about-content {
    padding: 4rem 0;
}

.about-content h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1e293b;
}

.about-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.about-text p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.capabilities h4 {
    margin-bottom: 1rem;
    color: #1e293b;
    font-size: 1.2rem;
}

.capabilities ul {
    list-style: none;
}

.capabilities li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #64748b;
}

.capabilities i {
    color: #10b981;
    width: 16px;
}

.about-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card h4 {
    font-size: 2rem;
    color: #4f46e5;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.stat-card p {
    color: #64748b;
    font-weight: 500;
}

/* Contact Section */
.contact-content {
    padding: 4rem 0;
}

.contact-content h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1e293b;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    margin-bottom: 2rem;
    color: #1e293b;
    font-size: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.contact-item i {
    font-size: 1.5rem;
    color: #4f46e5;
    width: 40px;
    text-align: center;
}

.contact-item h4 {
    margin-bottom: 0.25rem;
    color: #1e293b;
    font-weight: 600;
}

.contact-item p {
    color: #64748b;
    margin: 0;
}

.social-links {
    margin-top: 2rem;
}

.social-links h4 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background: #4f46e5;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    transform: scale(1.1);
    background: #4338ca;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.contact-form h3 {
    margin-bottom: 2rem;
    color: #1e293b;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4f46e5;
}

.form-group select {
    cursor: pointer;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: white;
}

.footer-section p {
    color: #94a3b8;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #94a3b8;
}

/* Loading and Typing Indicators */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: #64748b;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-grid,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .api-key-input {
        flex-direction: column;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .auth-section {
        order: -1;
    }
}

/* Auth Modal Styles */
.auth-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.auth-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.auth-modal-header h3 {
    margin: 0;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.auth-modal-body {
    padding: 1.5rem;
}

.auth-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.auth-tab {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    color: #64748b;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.auth-tab.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
}

.auth-tab:hover {
    color: #4f46e5;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form .form-group {
    margin-bottom: 1rem;
}

.auth-form input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.auth-form input:focus {
    outline: none;
    border-color: #4f46e5;
}

.auth-form .btn {
    width: 100%;
    margin-top: 0.5rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .chatbot-container {
        margin: 1rem;
    }

    .contact-form,
    .auth-prompt {
        padding: 1.5rem;
    }

    .auth-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .auth-modal-header,
    .auth-modal-body {
        padding: 1rem;
    }
}
