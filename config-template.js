// Auth0 Configuration Template
// Copy this to script.js and replace with your actual values

const AUTH0_CONFIG = {
    // Your Auth0 domain (found in Auth0 Dashboard > Applications > Your App > Settings)
    domain: 'your-auth0-domain.auth0.com',
    
    // Your Auth0 Client ID (found in Auth0 Dashboard > Applications > Your App > Settings)
    clientId: 'your-auth0-client-id',
    
    // Redirect URI (should match your app's URL)
    redirectUri: window.location.origin,
    
    // Optional: API Identifier if you're using Auth0 APIs
    audience: 'your-api-identifier',
    
    // Requested scopes
    scope: 'openid profile email'
};

// Example with real values (replace with your own):
/*
const AUTH0_CONFIG = {
    domain: 'gemini-ai-app.auth0.com',
    clientId: 'abc123def456ghi789jkl012',
    redirectUri: window.location.origin,
    scope: 'openid profile email'
};
*/

// Steps to get your Auth0 credentials:
// 1. Go to https://auth0.com and create a free account
// 2. Create a new application (Single Page Application)
// 3. Go to Applications > Your App > Settings
// 4. Copy the Domain and Client ID
// 5. Add your URLs to:
//    - Allowed Callback URLs
//    - Allowed Logout URLs  
//    - Allowed Web Origins
// 6. Replace the values above with your actual credentials
