# Gemini AI Platform with KeyAuth Authentication

A secure, modern web application that integrates Google's Gemini AI with KeyAuth authentication for robust security.

## 🚀 Features

### 🔐 **Secure Authentication**
- KeyAuth integration with license-based security
- Username/password and license key authentication
- Secure session management
- Protected access control

### 🤖 **AI-Powered Chatbot**
- Real-time conversations with Google's Gemini AI
- Typing indicators and smooth animations
- Message history with timestamps
- User avatar integration

### 📱 **Modern UI/UX**
- Responsive design for all devices
- Tab-based navigation (Home, Chatbot, About, Contact)
- Smooth animations and transitions
- Professional design with gradients

### 🛡️ **Security Features**
- Protected chatbot access (login required)
- Secure API key storage
- HTTPS-only API calls
- Privacy-first architecture

## 📋 Setup Instructions

### 1. KeyAuth Configuration

#### Create KeyAuth Account
1. Go to [KeyAuth.cc](https://keyauth.cc) and create an account
2. Create a new application in your KeyAuth dashboard
3. Configure your application settings

#### Get KeyAuth Credentials
1. Copy your **Owner ID** from your KeyAuth dashboard
2. Note your **Application Name**
3. Update `script.js` with your credentials:

```javascript
const KEYAUTH_CONFIG = {
    name: "YourAppName",           // Replace with your app name
    ownerid: "your-owner-id",      // Replace with your owner ID
    version: "1.0",                // Your app version
    url: "https://keyauth.win/api/1.2/"
};
```

#### Authentication Methods
The platform supports three authentication methods:
- **Login**: Username and password
- **Register**: Username, password, and license key
- **License Key**: Direct license activation

### 2. Gemini API Setup

#### Get Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy your API key (starts with "AIza...")

#### Configure API Key in App
1. Log in to the application
2. Navigate to the "Chatbot" tab
3. Enter your Gemini API key
4. Click "Set Key"
5. Start chatting!

### 3. Running the Application

#### Option A: Local Server (Recommended)
```bash
# Using Python
python -m http.server 8000

# Using Node.js
npx http-server

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

#### Option B: Direct File Access
Open `index.html` directly in your browser (some features may be limited).

## 🏗️ File Structure

```
├── index.html          # Main HTML structure with all tabs
├── styles.css          # Modern CSS with animations and responsive design
├── script.js           # JavaScript with Auth0 and Gemini API integration
└── README.md          # This setup guide
```

## 🎨 Customization

### Colors and Branding
Edit CSS variables in `styles.css`:
```css
:root {
    --primary-color: #4f46e5;
    --secondary-color: #667eea;
    --success-color: #10b981;
    --error-color: #ef4444;
}
```

### Auth0 Branding
1. In Auth0 dashboard, go to Branding
2. Upload your logo
3. Customize colors and fonts
4. Configure login/signup pages

### Content Updates
Update content in `index.html`:
- Hero section messaging
- Feature descriptions
- About section information
- Contact details

## 🔧 Advanced Configuration

### Social Connections
1. In Auth0 dashboard, go to Authentication > Social
2. Enable desired providers (Google, GitHub, LinkedIn, etc.)
3. Configure each provider with their credentials

### Multi-Factor Authentication
1. Go to Security > Multi-factor Auth
2. Enable desired MFA methods
3. Configure policies and rules

### Custom Domains
1. In Auth0 dashboard, go to Branding > Custom Domains
2. Add your custom domain
3. Update `AUTH0_CONFIG.domain` in `script.js`

## 🌐 Deployment

### Netlify
1. Connect your GitHub repository
2. Set build command: (none needed)
3. Set publish directory: `/`
4. Add environment variables if needed

### Vercel
1. Import your GitHub repository
2. Deploy with default settings
3. Update Auth0 URLs with your Vercel domain

### Traditional Hosting
1. Upload all files to your web server
2. Ensure HTTPS is enabled
3. Update Auth0 callback URLs

## 🔍 Troubleshooting

### Common Issues

#### "Authentication service not available"
- Check Auth0 configuration
- Verify domain and client ID
- Ensure callback URLs are correct

#### "Invalid API key" error
- Verify Gemini API key format
- Check if key is enabled in Google AI Studio
- Ensure you're logged in before setting the key

#### CORS errors
- Use a local server instead of file:// protocol
- Check Auth0 allowed origins
- Verify API endpoints

### Debug Mode
Open browser console to see detailed error messages and authentication status.

## 📊 Usage Analytics

### Auth0 Analytics
- Monitor user logins and registrations
- Track authentication methods
- View security events

### Custom Analytics
Add your preferred analytics service:
```javascript
// Example: Google Analytics
gtag('event', 'login', {
    'event_category': 'authentication',
    'event_label': 'auth0'
});
```

## 🔒 Security Best Practices

1. **Always use HTTPS in production**
2. **Keep Auth0 credentials secure**
3. **Regularly rotate API keys**
4. **Monitor authentication logs**
5. **Enable MFA for admin accounts**
6. **Use environment variables for sensitive data**

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

- **Auth0 Issues**: Check [Auth0 Documentation](https://auth0.com/docs)
- **Gemini API**: Visit [Google AI Documentation](https://ai.google.dev/docs)
- **General Issues**: Open a GitHub issue

---

**Built with ❤️ using Auth0 + Google Gemini AI**
